// shared/UI/components/notifications/ToastNotification.tsx
import React, { useCallback, useEffect, useState } from 'react';

export interface ToastNotificationProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  onClose: (_id: string) => void;
}

const ToastNotification: React.FC<ToastNotificationProps> = ({
  id: _id,
  type,
  title,
  message,
  duration = 5000,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [remainingTime, setRemainingTime] = useState(duration);

  const handleClose = useCallback(() => {
    setIsExiting(true);
    setTimeout(() => {
      onClose(_id);
    }, 300); // Match exit animation duration
  }, [_id, onClose]);

  const handleMouseEnter = useCallback(() => {
    setIsPaused(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsPaused(false);
  }, []);

  useEffect(() => {
    // Show animation
    const showTimer = setTimeout(() => setIsVisible(true), 100);

    return () => {
      clearTimeout(showTimer);
    };
  }, []);

  // Auto-close timer with pause functionality
  useEffect(() => {
    if (isPaused || isExiting) return;

    const interval = setInterval(() => {
      setRemainingTime(prev => {
        if (prev <= 100) {
          handleClose();
          return 0;
        }
        return prev - 100;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isPaused, isExiting, handleClose]);

  // Reset remaining time when paused state changes
  useEffect(() => {
    if (!isPaused && remainingTime !== duration) {
      // Don't reset if we're just starting
      return;
    }
  }, [isPaused, duration, remainingTime]);

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          bgColor: 'bg-success-notification',                    // Subtle green background with transparency
          borderColor: 'border-success-notification-border',     // Bright green border
          iconColor: 'text-success-notification-icon',           // Bright green icon
          titleColor: 'text-white',                              // White title for contrast
          messageColor: 'text-success-message',                  // Light green message text
          icon: 'ri-check-circle-line'
        };
      case 'error':
        return {
          bgColor: 'bg-error-notification',                      // Subtle red background with transparency
          borderColor: 'border-error-notification-border',       // Bright red border
          iconColor: 'text-error-notification-icon',             // Bright red icon
          titleColor: 'text-white',                              // White title for contrast
          messageColor: 'text-error-message',                    // Light red message text
          icon: 'ri-error-warning-line'
        };
      case 'warning':
        return {
          bgColor: 'bg-warning-notification',                    // Subtle orange background with transparency
          borderColor: 'border-warning-notification-border',     // Bright orange border
          iconColor: 'text-warning-notification-icon',           // Bright orange icon
          titleColor: 'text-white',                              // White title for contrast
          messageColor: 'text-warning-message',                  // Light orange message text
          icon: 'ri-alert-line'
        };
      case 'info':
      default:
        return {
          bgColor: 'bg-info-notification',                       // Slightly lighter dark background
          borderColor: 'border-info-notification-border',        // Bright blue border
          iconColor: 'text-info-notification-icon',              // Bright blue icon
          titleColor: 'text-white',                              // White title for contrast
          messageColor: 'text-info-message',                     // Light gray message text
          icon: 'ri-information-line'
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div
      className={`
        w-[380px] max-h-[120px]
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isExiting ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        ${isExiting ? 'translate-x-full opacity-0' : ''}
      `}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        className={`
          ${styles.bgColor} ${styles.borderColor}
          border-2 rounded-lg shadow-xl p-4 w-full max-h-[120px] overflow-hidden
           relative
          before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/5 before:to-transparent before:rounded-lg
          ${isPaused ? 'ring-2 ring-white/20' : ''}
        `}
      >
        <div className="flex items-start gap-3 h-full">
          <div className="flex-shrink-0 mt-0.5">
            <i className={`${styles.icon} ${styles.iconColor} text-lg`}></i>
          </div>
          <div className="flex-1 min-w-0 overflow-hidden">
            <h4 className={`${styles.titleColor} font-rubik font-semibold text-sm mb-1 truncate`}>
              {title}
            </h4>
            <p
              className={`${styles.messageColor} font-rubik text-sm leading-tight overflow-hidden`}
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                maxHeight: '2.5rem' // Approximately 2 lines
              }}
            >
              {message}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="flex-shrink-0 text-white/60 hover:text-white transition-all duration-200 p-1.5 hover:bg-white/10 rounded-full hover:scale-110 relative z-10"
            aria-label="Close notification"
          >
            <i className="ri-close-line text-sm"></i>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ToastNotification;
